package com.hmall.api.client;

import java.util.Collection;
import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.hmall.api.config.DefaultFeignConfig;
import com.hmall.api.dto.ItemDTO;

@FeignClient(value = "item-service", configuration = DefaultFeignConfig.class) // 注册中心对应的服务名称
public interface ItemClient {

    /**
     * 根据ids获取对应的商品信息
     * 
     * @param ids
     * @return
     */
    @GetMapping("/items")
    public List<ItemDTO> queryItemByIds(@RequestParam("ids") Collection<Long> ids);
}
