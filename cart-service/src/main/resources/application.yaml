server:
    port: 8082
spring:
    application:
        name: cart-service
    profiles:
        active: dev
    datasource:
        url: jdbc:mysql://${hm.db.host}:3306/hm-cart?useUnicode=true&characterEncoding=UTF-8&autoReconnect=true&serverTimezone=Asia/Shanghai
        driver-class-name: com.mysql.cj.jdbc.Driver
        username: root
        password: ${hm.db.pw}
    cloud:
        nacos:
            server-addr: localhost:8848
mybatis-plus:
    configuration:
        default-enum-type-handler: com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler
    global-config:
        db-config:
            update-strategy: not-null
            id-type: auto
logging:
    level:
        com.hmall: debug
    pattern:
        dateformat: HH:mm:ss:SSS
    file:
        path: "logs/${spring.application.name}"
knife4j:
    enable: true
    openapi:
        title: 黑马商城购物车服务接口文档
        description: "黑马商城购物车服务接口文档"
        email: z<PERSON><PERSON><EMAIL>
        concat: 虎哥
        url: https://www.itcast.cn
        version: v1.0.0
        group:
            default:
                group-name: default
                api-rule: package
                api-rule-resources:
                    - com.hmall.cart.controller
feign:
    okhttp:
        enabled: true
# keytool -genkeypair -alias hmall -keyalg RSA -keypass hmall123 -keystore hmall.jks -storepass hmall123
